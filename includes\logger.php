<?php
/**
 * AI Styled Image - Logging System
 */

defined('ABSPATH') || exit;

class AI_Styled_Logger {
    
    /**
     * Log an event
     */
    public static function log(string $event_type, string $message, array $context = []): void {
        global $wpdb;
        
        $log_data = [
            'event_type' => sanitize_text_field($event_type),
            'event_category' => sanitize_text_field($context['category'] ?? 'general'),
            'message' => sanitize_textarea_field($message),
            'context' => wp_json_encode($context),
            'user_id' => get_current_user_id() ?: null,
            'ip_address' => self::get_client_ip(),
            'user_agent' => sanitize_text_field($_SERVER['HTTP_USER_AGENT'] ?? ''),
            'request_data' => isset($context['request']) ? wp_json_encode($context['request']) : null,
            'response_data' => isset($context['response']) ? wp_json_encode($context['response']) : null,
            'execution_time' => $context['execution_time'] ?? null,
            'memory_usage' => $context['memory_usage'] ?? memory_get_usage(true),
            'created_at' => current_time('mysql')
        ];
        
        $wpdb->insert("{$wpdb->prefix}ai_styled_logs", $log_data);
    }
    
    /**
     * Log API request
     */
    public static function log_api_request(string $provider, string $endpoint, array $request_data, ?array $response_data, float $execution_time): void {
        $message = "API request to {$provider}: {$endpoint}";
        
        $context = [
            'category' => 'api',
            'provider' => $provider,
            'endpoint' => $endpoint,
            'request' => $request_data,
            'response' => $response_data,
            'execution_time' => $execution_time,
            'success' => $response_data !== null
        ];
        
        self::log('api_request', $message, $context);
    }
    
    /**
     * Log system event
     */
    public static function log_system_event(string $event, string $message, array $context = []): void {
        $context['category'] = 'system';
        self::log($event, $message, $context);
    }
    
    /**
     * Log error
     */
    public static function log_error(string $message, array $context = []): void {
        $context['category'] = 'error';
        self::log('error', $message, $context);
    }
    
    /**
     * Log user action
     */
    public static function log_user_action(string $action, string $message, array $context = []): void {
        $context['category'] = 'user_action';
        self::log($action, $message, $context);
    }
    
    /**
     * Log image processing
     */
    public static function log_image_processing(string $stage, string $message, array $context = []): void {
        $context['category'] = 'image_processing';
        self::log($stage, $message, $context);
    }
    
    /**
     * Get logs with pagination and filtering
     */
    public static function get_logs(array $args = []): array {
        global $wpdb;
        
        $defaults = [
            'limit' => 50,
            'offset' => 0,
            'event_type' => '',
            'event_category' => '',
            'date_from' => '',
            'date_to' => '',
            'search' => '',
            'order_by' => 'created_at',
            'order' => 'DESC'
        ];
        
        $args = wp_parse_args($args, $defaults);
        
        $where_conditions = ['1=1'];
        $where_values = [];
        
        if (!empty($args['event_type'])) {
            $where_conditions[] = 'event_type = %s';
            $where_values[] = $args['event_type'];
        }
        
        if (!empty($args['event_category'])) {
            $where_conditions[] = 'event_category = %s';
            $where_values[] = $args['event_category'];
        }
        
        if (!empty($args['date_from'])) {
            $where_conditions[] = 'created_at >= %s';
            $where_values[] = $args['date_from'];
        }
        
        if (!empty($args['date_to'])) {
            $where_conditions[] = 'created_at <= %s';
            $where_values[] = $args['date_to'];
        }
        
        if (!empty($args['search'])) {
            $where_conditions[] = '(message LIKE %s OR context LIKE %s)';
            $search_term = '%' . $wpdb->esc_like($args['search']) . '%';
            $where_values[] = $search_term;
            $where_values[] = $search_term;
        }
        
        $where_clause = implode(' AND ', $where_conditions);
        $order_clause = sprintf('ORDER BY %s %s', 
            sanitize_sql_orderby($args['order_by']), 
            $args['order'] === 'ASC' ? 'ASC' : 'DESC'
        );
        
        $limit_clause = sprintf('LIMIT %d OFFSET %d', 
            absint($args['limit']), 
            absint($args['offset'])
        );
        
        $query = "SELECT * FROM {$wpdb->prefix}ai_styled_logs WHERE {$where_clause} {$order_clause} {$limit_clause}";
        
        if (!empty($where_values)) {
            $query = $wpdb->prepare($query, $where_values);
        }
        
        $logs = $wpdb->get_results($query);

        // Check for database errors
        if ($wpdb->last_error) {
            error_log('AI Styled Image - Database error in get_logs: ' . $wpdb->last_error);
            return [
                'logs' => [],
                'total' => 0,
                'has_more' => false
            ];
        }

        // Get total count for pagination
        $count_query = "SELECT COUNT(*) FROM {$wpdb->prefix}ai_styled_logs WHERE {$where_clause}";
        if (!empty($where_values)) {
            $count_query = $wpdb->prepare($count_query, $where_values);
        }
        $total = $wpdb->get_var($count_query);

        // Handle null results gracefully
        if ($logs === null) {
            $logs = [];
        }
        if ($total === null) {
            $total = 0;
        }

        return [
            'logs' => $logs,
            'total' => intval($total),
            'has_more' => ($args['offset'] + $args['limit']) < intval($total)
        ];
    }
    
    /**
     * Clear logs
     */
    public static function clear_logs(array $filters = []): int {
        global $wpdb;
        
        if (empty($filters)) {
            // Clear all logs
            $deleted = $wpdb->query("DELETE FROM {$wpdb->prefix}ai_styled_logs");
        } else {
            // Clear logs with filters
            $where_conditions = [];
            $where_values = [];
            
            if (!empty($filters['older_than_days'])) {
                $where_conditions[] = 'created_at < DATE_SUB(NOW(), INTERVAL %d DAY)';
                $where_values[] = absint($filters['older_than_days']);
            }
            
            if (!empty($filters['event_category'])) {
                $where_conditions[] = 'event_category = %s';
                $where_values[] = $filters['event_category'];
            }
            
            if (!empty($where_conditions)) {
                $where_clause = implode(' AND ', $where_conditions);
                $query = "DELETE FROM {$wpdb->prefix}ai_styled_logs WHERE {$where_clause}";
                $deleted = $wpdb->query($wpdb->prepare($query, $where_values));
            } else {
                $deleted = 0;
            }
        }
        
        return $deleted ?: 0;
    }
    
    /**
     * Export logs to CSV
     */
    public static function export_logs_csv(array $filters = []): string {
        $logs_data = self::get_logs(array_merge($filters, ['limit' => 10000]));
        $logs = $logs_data['logs'];
        
        $csv_content = "ID,Event Type,Category,Message,User ID,IP Address,Created At\n";
        
        foreach ($logs as $log) {
            $csv_content .= sprintf(
                "%d,%s,%s,\"%s\",%s,%s,%s\n",
                $log->id,
                $log->event_type,
                $log->event_category,
                str_replace('"', '""', $log->message),
                $log->user_id ?: 'N/A',
                $log->ip_address ?: 'N/A',
                $log->created_at
            );
        }
        
        return $csv_content;
    }
    
    /**
     * Get client IP address
     */
    private static function get_client_ip(): string {
        $ip_keys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ip_keys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    }
    
    /**
     * Get log statistics
     */
    public static function get_log_stats(): array {
        global $wpdb;
        
        $stats = [
            'total_logs' => 0,
            'today_logs' => 0,
            'error_logs' => 0,
            'api_requests' => 0,
            'categories' => []
        ];
        
        // Check if table exists first
        $table_name = $wpdb->prefix . 'ai_styled_logs';
        if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") !== $table_name) {
            return $stats; // Return default stats if table doesn't exist
        }

        // Total logs
        $stats['total_logs'] = intval($wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}ai_styled_logs") ?: 0);

        // Today's logs
        $stats['today_logs'] = intval($wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}ai_styled_logs WHERE DATE(created_at) = CURDATE()") ?: 0);

        // Error logs
        $stats['error_logs'] = intval($wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}ai_styled_logs WHERE event_category = 'error'") ?: 0);

        // API requests
        $stats['api_requests'] = intval($wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}ai_styled_logs WHERE event_category = 'api'") ?: 0);

        // Categories breakdown
        $categories = $wpdb->get_results("SELECT event_category, COUNT(*) as count FROM {$wpdb->prefix}ai_styled_logs GROUP BY event_category ORDER BY count DESC");
        if ($categories) {
            foreach ($categories as $category) {
                $stats['categories'][$category->event_category] = intval($category->count);
            }
        }

        // Log any database errors
        if ($wpdb->last_error) {
            error_log('AI Styled Image - Database error in get_log_stats: ' . $wpdb->last_error);
        }
        
        return $stats;
    }
}
