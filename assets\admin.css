/* AI Styled Image - Next.js Inspired Modern Admin Styles */

:root {
  /* Colors - Next.js inspired palette */
  --ai-gray-50: #f9fafb;
  --ai-gray-100: #f3f4f6;
  --ai-gray-200: #e5e7eb;
  --ai-gray-300: #d1d5db;
  --ai-gray-400: #9ca3af;
  --ai-gray-500: #6b7280;
  --ai-gray-600: #4b5563;
  --ai-gray-700: #374151;
  --ai-gray-800: #1f2937;
  --ai-gray-900: #111827;
  
  --ai-blue-50: #eff6ff;
  --ai-blue-500: #3b82f6;
  --ai-blue-600: #2563eb;
  --ai-blue-700: #1d4ed8;
  
  --ai-green-50: #ecfdf5;
  --ai-green-500: #10b981;
  --ai-green-600: #059669;
  
  --ai-purple-50: #faf5ff;
  --ai-purple-500: #8b5cf6;
  --ai-purple-600: #7c3aed;
  
  --ai-red-50: #fef2f2;
  --ai-red-500: #ef4444;
  --ai-red-600: #dc2626;
  
  --ai-yellow-50: #fffbeb;
  --ai-yellow-500: #f59e0b;
  --ai-yellow-600: #d97706;
  
  /* Semantic colors */
  --ai-primary: var(--ai-blue-600);
  --ai-primary-hover: var(--ai-blue-700);
  --ai-success: var(--ai-green-500);
  --ai-warning: var(--ai-yellow-500);
  --ai-danger: var(--ai-red-500);
  
  /* Layout colors */
  --ai-background: var(--ai-gray-50);
  --ai-surface: #ffffff;
  --ai-border: var(--ai-gray-200);
  --ai-border-light: var(--ai-gray-100);
  
  /* Text colors */
  --ai-text-primary: var(--ai-gray-900);
  --ai-text-secondary: var(--ai-gray-600);
  --ai-text-muted: var(--ai-gray-500);
  
  /* Spacing */
  --ai-space-1: 0.25rem;
  --ai-space-2: 0.5rem;
  --ai-space-3: 0.75rem;
  --ai-space-4: 1rem;
  --ai-space-5: 1.25rem;
  --ai-space-6: 1.5rem;
  --ai-space-8: 2rem;
  --ai-space-10: 2.5rem;
  --ai-space-12: 3rem;
  --ai-space-16: 4rem;
  
  /* Border radius */
  --ai-radius-sm: 0.375rem;
  --ai-radius: 0.5rem;
  --ai-radius-md: 0.75rem;
  --ai-radius-lg: 1rem;
  --ai-radius-xl: 1.5rem;
  
  /* Shadows */
  --ai-shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --ai-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --ai-shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --ai-shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --ai-shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  
  /* Transitions */
  --ai-transition: all 0.15s ease-in-out;
  --ai-transition-fast: all 0.1s ease-in-out;
  --ai-transition-slow: all 0.3s ease-in-out;
  
  /* Typography */
  --ai-font-sans: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  --ai-font-mono: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
}

/* Reset and base styles */
* {
  box-sizing: border-box;
}

/* WordPress Compatible Admin Styles */
.ai-styled-admin-wrap {
  font-family: var(--ai-font-sans);
  line-height: 1.6;
  color: var(--ai-text-primary);
}

/* Tab Navigation */
.ai-nav-tab {
  background: none !important;
  border: none !important;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.ai-nav-tab .dashicons {
  font-size: 16px;
  width: 16px;
  height: 16px;
}

/* Stats Header */
.ai-stats-header {
  background: var(--ai-surface);
  border: 1px solid var(--ai-border);
  border-radius: var(--ai-radius);
  padding: var(--ai-space-6);
  margin: var(--ai-space-4) 0;
}



/* Stats Grid */
.ai-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--ai-space-6);
}

.ai-stat-card {
  background: var(--ai-surface);
  border: 1px solid var(--ai-border);
  border-radius: var(--ai-radius-lg);
  padding: var(--ai-space-6);
  display: flex;
  align-items: center;
  gap: var(--ai-space-4);
  transition: var(--ai-transition);
}

.ai-stat-card:hover {
  border-color: var(--ai-gray-300);
  box-shadow: var(--ai-shadow-md);
}

.ai-stat-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--ai-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.ai-stat-icon svg {
  width: 24px;
  height: 24px;
}

.ai-stat-icon-blue {
  background: var(--ai-blue-50);
  color: var(--ai-blue-600);
}

.ai-stat-icon-green {
  background: var(--ai-green-50);
  color: var(--ai-green-600);
}

.ai-stat-icon-purple {
  background: var(--ai-purple-50);
  color: var(--ai-purple-600);
}

.ai-stat-icon-success {
  background: var(--ai-green-50);
  color: var(--ai-green-600);
}

.ai-stat-icon-warning {
  background: var(--ai-yellow-50);
  color: var(--ai-yellow-600);
}

.ai-stat-content {
  flex: 1;
}

.ai-stat-number {
  font-size: 1.875rem;
  font-weight: 700;
  color: var(--ai-text-primary);
  line-height: 1;
  margin-bottom: var(--ai-space-1);
}

.ai-stat-label {
  font-size: 0.875rem;
  color: var(--ai-text-secondary);
  font-weight: 500;
}

/* Tab System */
.ai-tab-content {
  margin-top: var(--ai-space-4);
}

.ai-tab-panel {
  display: none;
}

.ai-tab-panel.active {
  display: block;
}

.ai-tab-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--ai-space-8);
  gap: var(--ai-space-6);
}

.ai-tab-title h2 {
  font-size: 1.875rem;
  font-weight: 700;
  color: var(--ai-text-primary);
  margin: 0 0 var(--ai-space-2) 0;
  letter-spacing: -0.025em;
}

.ai-tab-title p {
  color: var(--ai-text-secondary);
  margin: 0;
  font-size: 1rem;
}

.ai-tab-actions {
  display: flex;
  align-items: center;
  gap: var(--ai-space-4);
  flex-shrink: 0;
}

/* Buttons */
.ai-btn {
  display: inline-flex;
  align-items: center;
  gap: var(--ai-space-2);
  padding: var(--ai-space-3) var(--ai-space-4);
  border-radius: var(--ai-radius);
  border: 1px solid transparent;
  font-size: 0.875rem;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: var(--ai-transition);
  white-space: nowrap;
  background: none;
}

.ai-btn svg {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

.ai-btn-primary {
  background: var(--ai-primary);
  color: white;
  border-color: var(--ai-primary);
}

.ai-btn-primary:hover {
  background: var(--ai-primary-hover);
  border-color: var(--ai-primary-hover);
}

.ai-btn-secondary {
  background: var(--ai-surface);
  color: var(--ai-text-primary);
  border-color: var(--ai-border);
}

.ai-btn-secondary:hover {
  background: var(--ai-gray-50);
  border-color: var(--ai-gray-300);
}

.ai-btn-ghost {
  background: transparent;
  color: var(--ai-text-secondary);
  border-color: transparent;
}

.ai-btn-ghost:hover {
  background: var(--ai-gray-50);
  color: var(--ai-text-primary);
}

.ai-btn-danger {
  background: var(--ai-red-50);
  color: var(--ai-red-600);
  border-color: var(--ai-red-200);
}

.ai-btn-danger:hover {
  background: var(--ai-red-100);
  border-color: var(--ai-red-300);
}

.ai-btn-sm {
  padding: var(--ai-space-2) var(--ai-space-3);
  font-size: 0.75rem;
}

.ai-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Cards */
.ai-card {
  background: var(--ai-surface);
  border: 1px solid var(--ai-border);
  border-radius: var(--ai-radius-lg);
  overflow: hidden;
  box-shadow: var(--ai-shadow-sm);
}

.ai-card-header {
  padding: var(--ai-space-6);
  border-bottom: 1px solid var(--ai-border-light);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.ai-card-header h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--ai-text-primary);
  margin: 0;
}

.ai-card-content {
  padding: var(--ai-space-6);
}

/* Dashboard Grid */
.ai-dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--ai-space-6);
}

/* Activity List */
.ai-activity-list {
  display: flex;
  flex-direction: column;
  gap: var(--ai-space-3);
}

.ai-activity-item {
  display: flex;
  align-items: center;
  gap: var(--ai-space-3);
  padding: var(--ai-space-3);
  border-radius: var(--ai-radius);
  transition: var(--ai-transition);
}

.ai-activity-item:hover {
  background: var(--ai-gray-50);
}

.ai-activity-icon {
  width: 32px;
  height: 32px;
  border-radius: var(--ai-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.ai-activity-content {
  flex: 1;
}

.ai-activity-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--ai-text-primary);
  margin: 0 0 var(--ai-space-1) 0;
}

.ai-activity-time {
  font-size: 0.75rem;
  color: var(--ai-text-muted);
}

/* Storage Stats */
.ai-storage-stats {
  display: flex;
  flex-direction: column;
  gap: var(--ai-space-4);
}

.ai-storage-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.ai-storage-label {
  font-size: 0.875rem;
  color: var(--ai-text-secondary);
}

.ai-storage-value {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--ai-text-primary);
}

/* API Status */
.ai-api-status {
  display: flex;
  flex-direction: column;
  gap: var(--ai-space-4);
}

.ai-api-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.ai-api-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.ai-api-name {
  font-size: 0.875rem;
  color: var(--ai-text-secondary);
}

.ai-api-status-badge {
  font-size: 0.75rem;
  font-weight: 500;
  padding: var(--ai-space-1) var(--ai-space-2);
  border-radius: var(--ai-radius-sm);
}

.ai-status-active {
  background: var(--ai-green-50);
  color: var(--ai-green-600);
}

.ai-status-inactive {
  background: var(--ai-yellow-50);
  color: var(--ai-yellow-600);
}

/* Gallery */
.ai-gallery-container {
  margin-top: var(--ai-space-6);
}

.ai-gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: var(--ai-space-6);
  margin-bottom: var(--ai-space-8);
}

.ai-gallery-item {
  background: var(--ai-surface);
  border: 1px solid var(--ai-border);
  border-radius: var(--ai-radius-lg);
  overflow: hidden;
  transition: var(--ai-transition);
  cursor: pointer;
}

.ai-gallery-item:hover {
  border-color: var(--ai-gray-300);
  box-shadow: var(--ai-shadow-md);
}

.ai-gallery-image {
  position: relative;
  aspect-ratio: 16/9;
  overflow: hidden;
}

.ai-gallery-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.ai-gallery-actions {
  position: absolute;
  top: var(--ai-space-2);
  right: var(--ai-space-2);
  display: flex;
  gap: var(--ai-space-1);
  opacity: 0;
  transition: var(--ai-transition);
}

.ai-gallery-item:hover .ai-gallery-actions {
  opacity: 1;
}

.ai-gallery-action {
  width: 32px;
  height: 32px;
  border-radius: var(--ai-radius);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--ai-transition);
}

.ai-gallery-action:hover {
  background: rgba(0, 0, 0, 0.9);
}

.ai-gallery-action svg {
  width: 16px;
  height: 16px;
}

.ai-gallery-info {
  padding: var(--ai-space-4);
}

.ai-gallery-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--ai-text-primary);
  margin: 0 0 var(--ai-space-1) 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.ai-gallery-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.75rem;
  color: var(--ai-text-muted);
}

/* Overlays */
.ai-overlays-container {
  margin-top: var(--ai-space-6);
}

.ai-overlays-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--ai-space-6);
}

.ai-overlay-card {
  background: var(--ai-surface);
  border: 1px solid var(--ai-border);
  border-radius: var(--ai-radius-lg);
  overflow: hidden;
  transition: var(--ai-transition);
}

.ai-overlay-card:hover {
  border-color: var(--ai-gray-300);
  box-shadow: var(--ai-shadow-md);
}

.ai-overlay-image {
  position: relative;
  aspect-ratio: 4/3;
  overflow: hidden;
}

.ai-overlay-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.ai-overlay-content {
  padding: var(--ai-space-4);
}

.ai-overlay-content h4 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--ai-text-primary);
  margin: 0 0 var(--ai-space-2) 0;
}

.ai-overlay-category {
  font-size: 0.75rem;
  color: var(--ai-text-muted);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: var(--ai-space-2);
}

.ai-overlay-stats {
  font-size: 0.75rem;
  color: var(--ai-text-muted);
}

.ai-overlay-actions {
  padding: var(--ai-space-3) var(--ai-space-4);
  border-top: 1px solid var(--ai-border-light);
  display: flex;
  gap: var(--ai-space-2);
}

/* Logs */
.ai-logs-container {
  margin-top: var(--ai-space-6);
}

.ai-logs-filters {
  display: flex;
  gap: var(--ai-space-3);
  align-items: center;
}

.ai-logs-actions {
  display: flex;
  gap: var(--ai-space-2);
}

.ai-logs-list {
  background: var(--ai-surface);
  border: 1px solid var(--ai-border);
  border-radius: var(--ai-radius-lg);
  overflow: hidden;
}

.ai-log-item {
  padding: var(--ai-space-4);
  border-bottom: 1px solid var(--ai-border-light);
  display: flex;
  align-items: flex-start;
  gap: var(--ai-space-3);
}

.ai-log-item:last-child {
  border-bottom: none;
}

.ai-log-icon {
  width: 32px;
  height: 32px;
  border-radius: var(--ai-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.ai-log-icon-api {
  background: var(--ai-blue-50);
  color: var(--ai-blue-600);
}

.ai-log-icon-user {
  background: var(--ai-green-50);
  color: var(--ai-green-600);
}

.ai-log-icon-system {
  background: var(--ai-purple-50);
  color: var(--ai-purple-600);
}

.ai-log-icon-error {
  background: var(--ai-red-50);
  color: var(--ai-red-600);
}

.ai-log-content {
  flex: 1;
}

.ai-log-message {
  font-size: 0.875rem;
  color: var(--ai-text-primary);
  margin: 0 0 var(--ai-space-1) 0;
}

.ai-log-meta {
  display: flex;
  gap: var(--ai-space-4);
  font-size: 0.75rem;
  color: var(--ai-text-muted);
}

/* Forms */
.ai-settings-form {
  margin-top: var(--ai-space-6);
}

.ai-settings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--ai-space-6);
  margin-bottom: var(--ai-space-8);
}

.ai-form-group {
  margin-bottom: var(--ai-space-4);
}

.ai-form-group label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--ai-text-primary);
  margin-bottom: var(--ai-space-2);
}

.ai-input-field,
.ai-select {
  width: 100%;
  padding: var(--ai-space-3);
  border: 1px solid var(--ai-border);
  border-radius: var(--ai-radius);
  font-size: 0.875rem;
  background: var(--ai-surface);
  color: var(--ai-text-primary);
  transition: var(--ai-transition);
}

.ai-input-field:focus,
.ai-select:focus {
  outline: none;
  border-color: var(--ai-primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.ai-input-group {
  position: relative;
}

.ai-toggle-password {
  position: absolute;
  right: var(--ai-space-3);
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--ai-text-muted);
  cursor: pointer;
  padding: var(--ai-space-1);
}

.ai-field-help {
  font-size: 0.75rem;
  color: var(--ai-text-muted);
  margin-top: var(--ai-space-1);
}

.ai-field-help a {
  color: var(--ai-primary);
  text-decoration: none;
}

.ai-field-help a:hover {
  text-decoration: underline;
}

.ai-form-actions {
  display: flex;
  justify-content: flex-end;
  padding-top: var(--ai-space-6);
  border-top: 1px solid var(--ai-border-light);
}

/* Search */
.ai-search-box {
  position: relative;
  display: flex;
  align-items: center;
}

.ai-search-box svg {
  position: absolute;
  left: var(--ai-space-3);
  width: 16px;
  height: 16px;
  color: var(--ai-text-muted);
}

.ai-search-input {
  padding-left: var(--ai-space-8);
  width: 250px;
}

/* Modals */
.ai-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--ai-space-4);
}

.ai-modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

.ai-modal-container {
  position: relative;
  background: var(--ai-surface);
  border-radius: var(--ai-radius-lg);
  box-shadow: var(--ai-shadow-xl);
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow: hidden;
  animation: modalSlideIn 0.2s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.ai-modal-header {
  padding: var(--ai-space-6);
  border-bottom: 1px solid var(--ai-border-light);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.ai-modal-header h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--ai-text-primary);
  margin: 0;
}

.ai-close-btn {
  background: none;
  border: none;
  color: var(--ai-text-muted);
  cursor: pointer;
  padding: var(--ai-space-2);
  border-radius: var(--ai-radius);
  transition: var(--ai-transition);
}

.ai-close-btn:hover {
  background: var(--ai-gray-50);
  color: var(--ai-text-primary);
}

.ai-close-btn svg {
  width: 20px;
  height: 20px;
}

.ai-modal-body {
  padding: var(--ai-space-6);
  max-height: 60vh;
  overflow-y: auto;
}

.ai-modal-actions {
  padding: var(--ai-space-4) var(--ai-space-6) var(--ai-space-6);
  display: flex;
  gap: var(--ai-space-3);
  justify-content: flex-end;
}

/* Upload */
.ai-upload-dropzone {
  border: 2px dashed var(--ai-border);
  border-radius: var(--ai-radius-lg);
  padding: var(--ai-space-12);
  text-align: center;
  cursor: pointer;
  transition: var(--ai-transition);
  background: var(--ai-gray-50);
}

.ai-upload-dropzone:hover,
.ai-upload-dropzone.drag-over {
  border-color: var(--ai-primary);
  background: var(--ai-blue-50);
}

.ai-upload-icon {
  margin-bottom: var(--ai-space-4);
}

.ai-upload-icon svg {
  width: 48px;
  height: 48px;
  color: var(--ai-primary);
}

.ai-upload-dropzone h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--ai-text-primary);
  margin: 0 0 var(--ai-space-2) 0;
}

.ai-upload-dropzone p {
  color: var(--ai-text-secondary);
  margin: 0 0 var(--ai-space-3) 0;
}

.ai-upload-specs {
  font-size: 0.75rem;
  color: var(--ai-text-muted);
}

.ai-image-preview {
  display: flex;
  gap: var(--ai-space-4);
  align-items: center;
  padding: var(--ai-space-4);
  background: var(--ai-gray-50);
  border-radius: var(--ai-radius);
  margin-bottom: var(--ai-space-4);
}

.ai-preview-image img {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: var(--ai-radius);
}

.ai-preview-info {
  flex: 1;
}

.ai-preview-info h4 {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--ai-text-primary);
  margin: 0 0 var(--ai-space-1) 0;
}

.ai-preview-info p {
  font-size: 0.75rem;
  color: var(--ai-text-muted);
  margin: 0;
}

/* Pagination */
.ai-pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--ai-space-2);
  margin-top: var(--ai-space-6);
}

.ai-pagination-btn {
  padding: var(--ai-space-2) var(--ai-space-3);
  border: 1px solid var(--ai-border);
  background: var(--ai-surface);
  color: var(--ai-text-secondary);
  border-radius: var(--ai-radius);
  cursor: pointer;
  transition: var(--ai-transition);
  font-size: 0.875rem;
}

.ai-pagination-btn:hover {
  background: var(--ai-gray-50);
  border-color: var(--ai-gray-300);
}

.ai-pagination-btn.active {
  background: var(--ai-primary);
  color: white;
  border-color: var(--ai-primary);
}

.ai-pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Loading States */
.ai-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--ai-space-8);
  color: var(--ai-text-muted);
  font-size: 0.875rem;
}

.ai-loading::before {
  content: '';
  width: 20px;
  height: 20px;
  border: 2px solid var(--ai-border);
  border-top-color: var(--ai-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: var(--ai-space-2);
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Empty States */
.ai-empty-state {
  text-align: center;
  padding: var(--ai-space-12);
  color: var(--ai-text-muted);
}

.ai-empty-state svg {
  width: 64px;
  height: 64px;
  color: var(--ai-gray-300);
  margin-bottom: var(--ai-space-4);
}

.ai-empty-state h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--ai-text-primary);
  margin: 0 0 var(--ai-space-2) 0;
}

.ai-empty-state p {
  margin: 0 0 var(--ai-space-4) 0;
}

/* Lightbox */
.ai-lightbox {
  background: rgba(0, 0, 0, 0.9);
}

.ai-lightbox-container {
  position: relative;
  max-width: 90vw;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
}

.ai-lightbox-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.ai-lightbox-content img {
  max-width: 100%;
  max-height: 80vh;
  object-fit: contain;
  border-radius: var(--ai-radius);
}

.ai-lightbox-info {
  background: var(--ai-surface);
  padding: var(--ai-space-4);
  border-radius: var(--ai-radius);
  margin-top: var(--ai-space-4);
  text-align: center;
}

.ai-lightbox-info h4 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--ai-text-primary);
  margin: 0 0 var(--ai-space-1) 0;
}

.ai-lightbox-info p {
  font-size: 0.875rem;
  color: var(--ai-text-secondary);
  margin: 0;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .ai-stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }

  .ai-settings-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .ai-stats-header {
    padding: var(--ai-space-4);
  }

  .ai-tab-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--ai-space-4);
  }

  .ai-tab-actions {
    flex-wrap: wrap;
  }

  .ai-gallery-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }

  .ai-overlays-grid {
    grid-template-columns: 1fr;
  }

  .ai-dashboard-grid {
    grid-template-columns: 1fr;
  }

  .ai-logs-filters {
    flex-direction: column;
    align-items: stretch;
  }

  .ai-search-input {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .ai-modal {
    padding: var(--ai-space-2);
  }

  .ai-modal-container {
    max-width: 100%;
  }

  .ai-stats-grid {
    grid-template-columns: 1fr;
  }

  .ai-stat-card {
    flex-direction: column;
    text-align: center;
    gap: var(--ai-space-2);
  }

  .ai-stat-icon {
    align-self: center;
  }
}

/* Utilities */
.ai-sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.ai-hidden {
  display: none !important;
}

.ai-text-center {
  text-align: center;
}

.ai-text-right {
  text-align: right;
}

.ai-mb-0 {
  margin-bottom: 0 !important;
}

.ai-mt-4 {
  margin-top: var(--ai-space-4) !important;
}

.ai-p-0 {
  padding: 0 !important;
}
