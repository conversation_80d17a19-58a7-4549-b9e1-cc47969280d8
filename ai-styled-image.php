<?php
/**
 * Plugin Name: AI Styled Image
 * Plugin URI: https://moflavio.xyz/ai-styled-image
 * Description: Modern AI-powered image overlay tool using Replicate API for seamless architectural visualization integration
 * Version: 3.1.4
 * Author: Flavio
 * Author URI: https://moflavio.xyz
 * License: GPL v2 or later
 * Text Domain: ai-styled-image
 * Requires PHP: 7.4
 * Requires at least: 5.0
 */

defined('ABSPATH') || exit;

define('AI_STYLED_VERSION', '3.1.4');
define('AI_STYLED_URL', plugin_dir_url(__FILE__));
define('AI_STYLED_PATH', plugin_dir_path(__FILE__));
define('AI_STYLED_BASENAME', plugin_basename(__FILE__));

final class AIStyledImagePlugin {
    private static ?self $instance = null;
    
    public static function instance(): self {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        $this->init_hooks();
    }
    
    private function init_hooks(): void {
        add_action('init', [$this, 'init']);
        add_action('admin_menu', [$this, 'admin_menu']);
        add_action('wp_enqueue_scripts', [$this, 'frontend_assets']);
        add_action('admin_enqueue_scripts', [$this, 'admin_assets']);
        
        // Frontend AJAX endpoints
        add_action('wp_ajax_ai_styled_process', [$this, 'ajax_process_image']);
        add_action('wp_ajax_nopriv_ai_styled_process', [$this, 'ajax_process_image']);
        
        // Admin AJAX endpoints
        add_action('wp_ajax_ai_styled_upload_overlay', [$this, 'ajax_upload_overlay']);
        add_action('wp_ajax_ai_styled_delete_overlay', [$this, 'ajax_delete_overlay']);
        add_action('wp_ajax_ai_styled_get_overlay', [$this, 'ajax_get_overlay']);
        add_action('wp_ajax_ai_styled_get_overlays', [$this, 'ajax_get_overlays']);
        add_action('wp_ajax_ai_styled_save_identity', [$this, 'ajax_save_identity']);

        // New admin AJAX endpoints for gallery and logs
        add_action('wp_ajax_ai_styled_get_gallery', [$this, 'ajax_get_gallery']);
        add_action('wp_ajax_ai_styled_delete_gallery_image', [$this, 'ajax_delete_gallery_image']);
        add_action('wp_ajax_ai_styled_get_logs', [$this, 'ajax_get_logs']);
        add_action('wp_ajax_ai_styled_clear_logs', [$this, 'ajax_clear_logs']);
        add_action('wp_ajax_ai_styled_download_logs', [$this, 'ajax_download_logs']);
        
        add_shortcode('ai_image_tool', [$this, 'shortcode']);
        register_activation_hook(__FILE__, [$this, 'activate']);
        register_deactivation_hook(__FILE__, [$this, 'deactivate']);

        // Log plugin activation
        if (!get_option('ai_styled_activated_logged', false)) {
            require_once AI_STYLED_PATH . 'includes/logger.php';
            AI_Styled_Logger::log_system_event(
                'plugin_activated',
                'AI Styled Image plugin was activated',
                ['version' => AI_STYLED_VERSION]
            );
            update_option('ai_styled_activated_logged', true);
        }
    }

    public function init(): void {
        load_plugin_textdomain('ai-styled-image', false, dirname(AI_STYLED_BASENAME) . '/languages');
    }
    
    public function activate(): void {
        $this->create_tables();
        $this->set_defaults();
        flush_rewrite_rules();

        // Add initial log entry
        require_once AI_STYLED_PATH . 'includes/logger.php';
        AI_Styled_Logger::log_system_event(
            'plugin_activated',
            'AI Styled Image plugin was activated successfully',
            ['version' => AI_STYLED_VERSION, 'timestamp' => current_time('mysql')]
        );
    }
    
    public function deactivate(): void {
        flush_rewrite_rules();
    }
    
    private function create_tables(): void {
        global $wpdb;

        // Create overlays table
        $sql_overlays = "CREATE TABLE IF NOT EXISTS {$wpdb->prefix}ai_overlays (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            title varchar(255) NOT NULL,
            description text,
            image_url varchar(500) NOT NULL,
            category varchar(100) DEFAULT 'general',
            prompt_template text,
            usage_count int(11) DEFAULT 0,
            created_at timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY category (category),
            KEY usage_count (usage_count)
        ) {$wpdb->get_charset_collate()};";

        // Create logs table
        $sql_logs = "CREATE TABLE IF NOT EXISTS {$wpdb->prefix}ai_styled_logs (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            event_type varchar(50) NOT NULL,
            event_category varchar(50) NOT NULL DEFAULT 'general',
            message text NOT NULL,
            context longtext,
            user_id bigint(20) unsigned DEFAULT NULL,
            ip_address varchar(45) DEFAULT NULL,
            user_agent text DEFAULT NULL,
            request_data longtext DEFAULT NULL,
            response_data longtext DEFAULT NULL,
            execution_time float DEFAULT NULL,
            memory_usage bigint(20) DEFAULT NULL,
            created_at timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY event_type (event_type),
            KEY event_category (event_category),
            KEY user_id (user_id),
            KEY created_at (created_at)
        ) {$wpdb->get_charset_collate()};";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        $result_overlays = dbDelta($sql_overlays);
        $result_logs = dbDelta($sql_logs);

        // Log table creation results for debugging
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('AI Styled Image - Table creation results:');
            error_log('Overlays: ' . print_r($result_overlays, true));
            error_log('Logs: ' . print_r($result_logs, true));
        }

        $this->create_demo_overlays();
    }
    
    private function create_demo_overlays(): void {
        global $wpdb;
        
        if ($wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}ai_overlays") > 0) {
            return;
        }
        
        $overlays = [
            [
                'title' => 'Modern Glass Room',
                'description' => 'Sleek glass conservatory for seamless garden integration',
                'image_url' => 'data:image/svg+xml;base64,' . base64_encode($this->get_demo_svg('glass-room')),
                'category' => 'Glass Room',
                'prompt_template' => 'Integrate a sleek, modern glass room into the existing garden environment in a way that looks architecturally intentional and visually seamless. Place the glass room in the left corner of the garden, adjacent to the side fence and facing the main patio, ensuring no disruption to the original layout. Keep all existing elements intact—such as the house structure, landscaping, fence, lawn, trees, and any garden furniture. The glass room should harmonize with the overall style of the house and garden, using realistic reflections that mirror nearby plants and structures. Match the lighting angle, brightness, and color tone precisely with the original photo. Ensure the structure casts natural, consistent shadows and feels proportionate to the surrounding space. The result should resemble a professionally planned extension that fits naturally into the existing scene.'
            ],
            [
                'title' => 'Modern Veranda',
                'description' => 'Contemporary veranda structure for outdoor living',
                'image_url' => 'data:image/svg+xml;base64,' . base64_encode($this->get_demo_svg('pergola')),
                'category' => 'Veranda',
                'prompt_template' => 'Seamlessly integrate the modern veranda structure into the existing garden scene without altering any original elements of the base image. Position the veranda attached to the back wall of the house, extending over the existing patio area, while preserving all existing features including: house architecture, fence design, lawn areas, existing plants, patio elements, and garden furniture. The veranda should appear as a natural extension that complements the space. Match the lighting conditions, shadow direction, and color temperature of the original garden photo. Ensure the veranda\'s glass and metal surfaces reflect the surrounding garden environment accurately. Maintain the exact proportions and design details of both the original garden layout and the veranda structure. The integration should look professionally planned and architecturally coherent.'
            ],
            [
                'title' => 'Shading System',
                'description' => 'Modern shading system for outdoor comfort',
                'image_url' => 'data:image/svg+xml;base64,' . base64_encode($this->get_demo_svg('pool')),
                'category' => 'Shading System',
                'prompt_template' => 'Seamlessly integrate a modern shading system into the existing garden scene without altering any original elements of the base image. Position the shading system over the seating area on the wooden deck near the right-side fence, while preserving all existing features including: house architecture, fence design, lawn areas, existing plants, patio elements, and garden furniture. The shading system should appear as a functional and stylish addition that enhances comfort and complements the outdoor space. Match the lighting conditions, shadow direction, and color temperature of the original garden photo. Ensure the shading system casts realistic shadows and interacts naturally with surrounding structures. Maintain the exact proportions and visual coherence between the shading system and the garden layout. The integration should look professionally installed and seamlessly blended.'
            ]
        ];
        
        foreach ($overlays as $overlay) {
            $wpdb->insert("{$wpdb->prefix}ai_overlays", $overlay);
        }
    }
    
    private function get_demo_svg(string $type): string {
        $svgs = [
            'glass-room' => '<svg viewBox="0 0 200 150" xmlns="http://www.w3.org/2000/svg"><rect width="200" height="150" fill="#f8fafc" stroke="#e2e8f0" stroke-width="2"/><rect x="20" y="30" width="160" height="90" fill="none" stroke="#0f172a" stroke-width="2" opacity="0.8"/><text x="100" y="80" text-anchor="middle" fill="#0f172a" font-family="Inter, sans-serif" font-size="14" font-weight="500">Glass Room</text></svg>',
            'pergola' => '<svg viewBox="0 0 200 150" xmlns="http://www.w3.org/2000/svg"><rect width="200" height="150" fill="#f8fafc" stroke="#e2e8f0" stroke-width="2"/><rect x="30" y="40" width="140" height="70" fill="none" stroke="#0f172a" stroke-width="2" opacity="0.8"/><text x="100" y="80" text-anchor="middle" fill="#0f172a" font-family="Inter, sans-serif" font-size="14" font-weight="500">Veranda</text></svg>',
            'pool' => '<svg viewBox="0 0 200 150" xmlns="http://www.w3.org/2000/svg"><rect width="200" height="150" fill="#f8fafc" stroke="#e2e8f0" stroke-width="2"/><rect x="40" y="50" width="120" height="50" fill="none" stroke="#0f172a" stroke-width="2" opacity="0.8"/><rect x="50" y="60" width="100" height="30" fill="#0f172a" opacity="0.3"/><text x="100" y="80" text-anchor="middle" fill="#0f172a" font-family="Inter, sans-serif" font-size="12" font-weight="500">Shading</text></svg>'
        ];
        
        return $svgs[$type] ?? $svgs['glass-room'];
    }
    
    private function set_defaults(): void {
        $defaults = [
            'api_token' => '',
            'model_endpoint' => 'flux-kontext-apps/multi-image-kontext-pro',
            'max_file_size' => 10485760,
            'rate_limit' => 50,
            'allowed_formats' => ['jpg', 'jpeg', 'png', 'webp'],
            'openrouter_api_key' => '',
            'openrouter_model' => 'openai/gpt-4.1',
            'processing_mode' => 'new', // 'current' or 'new'
            'identity_settings' => [
                'primary_color' => '#0f172a',
                'secondary_color' => '#3b82f6',
                'accent_color' => '#10b981',
                'background_color' => '#f8fafc',
                'surface_color' => '#ffffff',
                'text_color' => '#1e293b',
                'muted_color' => '#64748b',
                'border_color' => '#e2e8f0',
                'brand_name' => 'AI STYLED',
                'font_family' => 'Inter',
                'font_weight' => '500',
                'border_radius' => '8',
                'form_style' => 'modern'
            ]
        ];
        
        foreach ($defaults as $key => $value) {
            add_option("ai_styled_{$key}", $value);
        }
    }
    
    public function admin_menu(): void {
        add_menu_page(
            __('AI Styled Image', 'ai-styled-image'),
            __('AI Styled Image', 'ai-styled-image'),
            'manage_options',
            'ai-styled-image',
            [$this, 'admin_page'],
            'data:image/svg+xml;base64,' . base64_encode('<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" xmlns="http://www.w3.org/2000/svg"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"/><circle cx="8.5" cy="8.5" r="1.5"/><path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"/></svg>'),
            30
        );
    }
    
    public function admin_page(): void {
        require_once AI_STYLED_PATH . 'includes/admin.php';
    }
    
    public function frontend_assets(): void {
        wp_enqueue_style('ai-styled-frontend', AI_STYLED_URL . 'assets/style.css', [], AI_STYLED_VERSION);
        wp_enqueue_script('ai-styled-frontend', AI_STYLED_URL . 'assets/script.js', [], AI_STYLED_VERSION, true);
        
        wp_localize_script('ai-styled-frontend', 'aiImageTool', [
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('ai_styled_frontend'),
            'maxFileSize' => get_option('ai_styled_max_file_size', 10485760),
            'allowedFormats' => get_option('ai_styled_allowed_formats', ['jpg', 'jpeg', 'png', 'webp']),
            'processingMode' => get_option('ai_styled_processing_mode', 'new'),
            'identity' => $this->get_identity_settings(),
            'strings' => [
                'uploading' => __('Uploading...', 'ai-styled-image'),
                'processing' => __('Processing with AI...', 'ai-styled-image'),
                'error' => __('Something went wrong. Please try again.', 'ai-styled-image'),
                'success' => __('Image generated successfully!', 'ai-styled-image')
            ]
        ]);
    }
    
    public function admin_assets(string $hook): void {
        if ($hook !== 'toplevel_page_ai-styled-image') {
            return;
        }
        
        wp_enqueue_media();
        wp_enqueue_style('ai-styled-admin', AI_STYLED_URL . 'assets/admin.css', [], AI_STYLED_VERSION);
        wp_enqueue_script('ai-styled-admin', AI_STYLED_URL . 'assets/admin.js', ['jquery', 'wp-util', 'media-upload'], AI_STYLED_VERSION, true);
        
        wp_localize_script('ai-styled-admin', 'ai_styled_admin', [
            'ajaxurl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('ai_styled_admin'),
            'identity' => $this->get_identity_settings()
        ]);

        // Also add the global ajaxurl for compatibility
        wp_localize_script('ai-styled-admin', 'ajaxurl', admin_url('admin-ajax.php'));
    }
    
    public function shortcode(array $atts = []): string {
        $atts = shortcode_atts([
            'class' => '',
            'style' => 'modern',
            'mode' => 'normal'
        ], $atts, 'ai_image_tool');

        ob_start();
        require AI_STYLED_PATH . 'includes/frontend.php';
        return ob_get_clean();
    }
    
    public function ajax_process_image(): void {
        require_once AI_STYLED_PATH . 'includes/processor.php';
        $processor = new AI_Image_Processor();
        $processor->ajax_process_image();
    }
    
    public function ajax_upload_overlay(): void {
        require_once AI_STYLED_PATH . 'includes/processor.php';
        $processor = new AI_Image_Processor();
        $processor->ajax_upload_overlay();
    }
    
    public function ajax_delete_overlay(): void {
        require_once AI_STYLED_PATH . 'includes/processor.php';
        $processor = new AI_Image_Processor();
        $processor->ajax_delete_overlay();
    }
    
    public function ajax_get_overlay(): void {
        require_once AI_STYLED_PATH . 'includes/processor.php';
        $processor = new AI_Image_Processor();
        $processor->ajax_get_overlay();
    }
    
    public function ajax_get_overlays(): void {
        require_once AI_STYLED_PATH . 'includes/processor.php';
        $processor = new AI_Image_Processor();
        $processor->ajax_get_overlays();
    }
    
    public function ajax_save_identity(): void {
        check_ajax_referer('ai_styled_admin', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }
        
        $identity_settings = $_POST['identity_settings'] ?? [];
        
        $sanitized_settings = [
            'primary_color' => sanitize_hex_color($identity_settings['primary_color'] ?? '#0f172a'),
            'secondary_color' => sanitize_hex_color($identity_settings['secondary_color'] ?? '#3b82f6'),
            'accent_color' => sanitize_hex_color($identity_settings['accent_color'] ?? '#10b981'),
            'background_color' => sanitize_hex_color($identity_settings['background_color'] ?? '#f8fafc'),
            'surface_color' => sanitize_hex_color($identity_settings['surface_color'] ?? '#ffffff'),
            'text_color' => sanitize_hex_color($identity_settings['text_color'] ?? '#1e293b'),
            'muted_color' => sanitize_hex_color($identity_settings['muted_color'] ?? '#64748b'),
            'border_color' => sanitize_hex_color($identity_settings['border_color'] ?? '#e2e8f0'),
            'brand_name' => sanitize_text_field($identity_settings['brand_name'] ?? 'AI STYLED'),
            'font_family' => sanitize_text_field($identity_settings['font_family'] ?? 'Inter'),
            'font_weight' => sanitize_text_field($identity_settings['font_weight'] ?? '500'),
            'border_radius' => absint($identity_settings['border_radius'] ?? 8),
            'form_style' => sanitize_text_field($identity_settings['form_style'] ?? 'modern')
        ];
        
        update_option('ai_styled_identity_settings', $sanitized_settings);
        
        wp_send_json_success(['message' => 'Identity settings saved successfully']);
    }
    
    public function get_identity_settings(): array {
        $defaults = [
            'primary_color' => '#0f172a',
            'secondary_color' => '#3b82f6',
            'accent_color' => '#10b981',
            'background_color' => '#f8fafc',
            'surface_color' => '#ffffff',
            'text_color' => '#1e293b',
            'muted_color' => '#64748b',
            'border_color' => '#e2e8f0',
            'brand_name' => 'AI STYLED',
            'font_family' => 'Inter',
            'font_weight' => '500',
            'border_radius' => 8,
            'form_style' => 'modern'
        ];

        return get_option('ai_styled_identity_settings', $defaults);
    }

    /**
     * AJAX: Get gallery images
     */
    public function ajax_get_gallery(): void {
        check_ajax_referer('ai_styled_admin', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        require_once AI_STYLED_PATH . 'includes/gallery.php';

        $args = [
            'limit' => absint($_POST['limit'] ?? 24),
            'offset' => absint($_POST['offset'] ?? 0),
            'order_by' => sanitize_text_field($_POST['order_by'] ?? 'date'),
            'order' => sanitize_text_field($_POST['order'] ?? 'DESC'),
            'search' => sanitize_text_field($_POST['search'] ?? '')
        ];

        $gallery_data = AI_Styled_Gallery::get_gallery_images($args);
        $stats = AI_Styled_Gallery::get_gallery_stats();

        wp_send_json_success([
            'images' => $gallery_data['images'],
            'total' => $gallery_data['total'],
            'has_more' => $gallery_data['has_more'],
            'stats' => $stats
        ]);
    }

    /**
     * AJAX: Delete gallery image
     */
    public function ajax_delete_gallery_image(): void {
        check_ajax_referer('ai_styled_admin', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        $image_id = sanitize_text_field($_POST['image_id'] ?? '');

        if (empty($image_id)) {
            wp_send_json_error('Image ID is required');
        }

        require_once AI_STYLED_PATH . 'includes/gallery.php';
        $result = AI_Styled_Gallery::delete_image($image_id);

        if ($result['success']) {
            wp_send_json_success($result);
        } else {
            wp_send_json_error($result['message']);
        }
    }

    /**
     * AJAX: Get logs
     */
    public function ajax_get_logs(): void {
        try {
            check_ajax_referer('ai_styled_admin', 'nonce');

            if (!current_user_can('manage_options')) {
                wp_send_json_error('Unauthorized access');
                return;
            }

            require_once AI_STYLED_PATH . 'includes/logger.php';

            // Check if logs table exists
            global $wpdb;
            $table_name = $wpdb->prefix . 'ai_styled_logs';
            if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") !== $table_name) {
                // Create table if it doesn't exist
                $this->create_tables();
            }

            $args = [
                'limit' => absint($_POST['limit'] ?? 50),
                'offset' => absint($_POST['offset'] ?? 0),
                'event_type' => sanitize_text_field($_POST['event_type'] ?? ''),
                'event_category' => sanitize_text_field($_POST['event_category'] ?? ''),
                'search' => sanitize_text_field($_POST['search'] ?? ''),
                'date_from' => sanitize_text_field($_POST['date_from'] ?? ''),
                'date_to' => sanitize_text_field($_POST['date_to'] ?? '')
            ];

            $logs_data = AI_Styled_Logger::get_logs($args);
            $stats = AI_Styled_Logger::get_log_stats();

            wp_send_json_success([
                'logs' => $logs_data['logs'],
                'total' => $logs_data['total'],
                'has_more' => $logs_data['has_more'],
                'stats' => $stats
            ]);
        } catch (Exception $e) {
            wp_send_json_error('Failed to load logs: ' . $e->getMessage());
        }
    }

    /**
     * AJAX: Clear logs
     */
    public function ajax_clear_logs(): void {
        check_ajax_referer('ai_styled_admin', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        require_once AI_STYLED_PATH . 'includes/logger.php';

        $filters = [];
        if (!empty($_POST['older_than_days'])) {
            $filters['older_than_days'] = absint($_POST['older_than_days']);
        }
        if (!empty($_POST['event_category'])) {
            $filters['event_category'] = sanitize_text_field($_POST['event_category']);
        }

        $deleted_count = AI_Styled_Logger::clear_logs($filters);

        AI_Styled_Logger::log_user_action(
            'logs_cleared',
            "Cleared {$deleted_count} log entries",
            ['filters' => $filters, 'deleted_count' => $deleted_count]
        );

        wp_send_json_success([
            'message' => "Successfully cleared {$deleted_count} log entries",
            'deleted_count' => $deleted_count
        ]);
    }

    /**
     * AJAX: Download logs
     */
    public function ajax_download_logs(): void {
        check_ajax_referer('ai_styled_admin', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        require_once AI_STYLED_PATH . 'includes/logger.php';

        $filters = [
            'event_type' => sanitize_text_field($_POST['event_type'] ?? ''),
            'event_category' => sanitize_text_field($_POST['event_category'] ?? ''),
            'date_from' => sanitize_text_field($_POST['date_from'] ?? ''),
            'date_to' => sanitize_text_field($_POST['date_to'] ?? '')
        ];

        $csv_content = AI_Styled_Logger::export_logs_csv($filters);
        $filename = 'ai-styled-logs-' . date('Y-m-d-H-i-s') . '.csv';

        AI_Styled_Logger::log_user_action(
            'logs_downloaded',
            "Downloaded logs as CSV: {$filename}",
            ['filters' => $filters]
        );

        wp_send_json_success([
            'csv_content' => $csv_content,
            'filename' => $filename
        ]);
    }
}

AIStyledImagePlugin::instance(); 